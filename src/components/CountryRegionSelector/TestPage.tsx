import React from 'react';
import { Form, Button, Card } from 'antd';
import CountrySelector from './CountrySelector';
import RegionSelector from './RegionSelector';

const TestPage: React.FC = () => {
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    console.log('表单值:', values);
  };

  const onValuesChange = (changedValues: any, allValues: any) => {
    console.log('字段变化:', changedValues);
    console.log('所有值:', allValues);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <Card title="国家地区选择器测试">
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          onValuesChange={onValuesChange}
          autoComplete="off"
        >
          <Form.Item
            label="选择国家"
            name="country"
            rules={[{ required: true, message: '请选择国家' }]}
          >
            <CountrySelector placeholder="请选择国家" />
          </Form.Item>

          <Form.Item
            label="选择地区"
            name="region"
            rules={[{ required: true, message: '请选择地区' }]}
          >
            <RegionSelector
              form={form}
              placeholder="请选择地区"
              countryFieldName="country"
              regionFieldName="region"
            />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit">
              提交
            </Button>
            <Button 
              style={{ marginLeft: '10px' }} 
              onClick={() => form.resetFields()}
            >
              重置
            </Button>
          </Form.Item>
        </Form>

        <div style={{ marginTop: '20px' }}>
          <h3>使用说明：</h3>
          <ol>
            <li>首先选择一个国家</li>
            <li>地区选择器会自动更新为该国家的地区选项</li>
            <li>当切换国家时，地区选择会自动清空（如果当前地区不属于新国家）</li>
            <li>查看控制台输出来观察表单值的变化</li>
          </ol>
        </div>
      </Card>
    </div>
  );
};

export default TestPage;
