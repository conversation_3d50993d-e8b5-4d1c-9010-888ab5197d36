import React from 'react';
import { Select } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import { countryRegionOptions } from './countryRegionData';

interface CountrySelectorProps {
  placeholder?: string;
  size?: 'small' | 'middle' | 'large';
}
const CountrySelector: React.FC<CountrySelectorProps> = ({
  placeholder,
  size = 'large',
}) => {
  const { t } = useLanguage();
  const options = countryRegionOptions.map(item => ({
    label: item.label,
    value: item.value,
  }));
  return (
    <Select
      placeholder={placeholder || t('common.form.selectCountry')}
      size={size}
      className="!h-54px text-14px flex-shrink-0 flex-basis-88px"
      options={options}
    />
  );
};

export default CountrySelector;
