import React from 'react';
import { type FormInstance, Select, Form } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import { countryRegionOptions } from './countryRegionData';

interface RegionSelectorProps {
  placeholder?: string;
  size?: 'small' | 'middle' | 'large';
  form: FormInstance;
  countryFieldName?: string; // 允许自定义 country 字段名
  regionFieldName?: string; // 允许自定义 region 字段名
}

const RegionSelector: React.FC<RegionSelectorProps> = ({
  placeholder,
  size = 'large',
  form,
  countryFieldName = 'country',
  regionFieldName = 'region',
}) => {
  const { t } = useLanguage();

  return (
    <Form.Item
      noStyle
      shouldUpdate={(prevValues, currentValues) =>
        prevValues[countryFieldName] !== currentValues[countryFieldName]
      }
    >
      {({ getFieldValue, setFieldValue }) => {
        const country = getFieldValue(countryFieldName);
        const options = country
          ? countryRegionOptions.find(item => item.value === country)
              ?.children || []
          : [];

        // 当国家变化时，清空地区选择
        const handleCountryChange = () => {
          const currentRegion = getFieldValue(regionFieldName);
          if (currentRegion && country) {
            // 检查当前选择的地区是否还在新国家的选项中
            const isValidRegion = options.some(
              option => option.value === currentRegion
            );
            if (!isValidRegion) {
              setFieldValue(regionFieldName, undefined);
            }
          }
        };

        // 在渲染时检查并清理无效的地区选择
        React.useEffect(() => {
          handleCountryChange();
        }, [country]);

        return (
          <Select
            placeholder={placeholder || t('common.form.selectRegion')}
            size={size}
            className="!h-54px text-14px flex-shrink-0 flex-basis-88px"
            options={options}
            disabled={!country} // 当没有选择国家时禁用
          />
        );
      }}
    </Form.Item>
  );
};
export default RegionSelector;
