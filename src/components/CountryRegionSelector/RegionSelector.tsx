import React, { useRef } from 'react';
import { type FormInstance, Select, Form } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import { countryRegionOptions } from './countryRegionData';

interface RegionSelectorProps {
  placeholder?: string;
  size?: 'small' | 'middle' | 'large';
  form: FormInstance;
  countryFieldName?: string; // 允许自定义 country 字段名
  regionFieldName?: string; // 允许自定义 region 字段名
}

const RegionSelector: React.FC<RegionSelectorProps> = ({
  placeholder,
  size = 'large',
  form,
  countryFieldName = 'country',
  regionFieldName = 'region',
}) => {
  const { t } = useLanguage();
  const prevCountryRef = useRef<string | undefined>(undefined);

  return (
    <Form.Item
      noStyle
      shouldUpdate={(prevValues, currentValues) =>
        prevValues[countryFieldName] !== currentValues[countryFieldName]
      }
    >
      {({ getFieldValue, setFieldValue }) => {
        const country = getFieldValue(countryFieldName);
        const options = country
          ? countryRegionOptions.find(item => item.value === country)
              ?.children || []
          : [];

        // 检查国家是否发生了变化
        if (prevCountryRef.current !== country) {
          const currentRegion = getFieldValue(regionFieldName);

          // 如果有当前地区选择且国家发生了变化
          if (currentRegion && prevCountryRef.current !== undefined) {
            // 检查当前选择的地区是否还在新国家的选项中
            const isValidRegion = options.some(
              option => option.value === currentRegion
            );
            if (!isValidRegion) {
              // 异步清空地区选择，避免在渲染过程中修改状态
              setTimeout(() => {
                setFieldValue(regionFieldName, undefined);
              }, 0);
            }
          }

          // 更新上一次的国家值
          prevCountryRef.current = country;
        }

        return (
          <Select
            placeholder={placeholder || t('common.form.selectRegion')}
            size={size}
            className="!h-54px text-14px flex-shrink-0 flex-basis-88px"
            options={options}
            disabled // 当没有选择国家时禁用
          />
        );
      }}
    </Form.Item>
  );
};

// disabled={!country} // 当没有选择国家时禁用
export default RegionSelector;
