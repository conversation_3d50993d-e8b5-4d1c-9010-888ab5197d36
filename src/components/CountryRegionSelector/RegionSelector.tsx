import React, { useMemo } from 'react';
import { type FormInstance, Select } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import { countryRegionOptions } from './countryRegionData';

interface RegionSelectorProps {
  placeholder?: string;
  size?: 'small' | 'middle' | 'large';
  form: FormInstance;
}
const RegionSelector: React.FC<RegionSelectorProps> = ({
  placeholder,
  size = 'large',
  form,
}) => {
  const { t } = useLanguage();
  const options = useMemo(() => {
    const country = form.getFieldValue('country');
    return countryRegionOptions.find(item => item.value === country)?.children;
  }, [form]);
  console.log(options);
  return (
    <Select
      placeholder={placeholder || t('common.form.selectRegion')}
      size={size}
      className="!h-54px text-14px flex-shrink-0 flex-basis-88px"
      options={options}
    />
  );
};
export default RegionSelector;
