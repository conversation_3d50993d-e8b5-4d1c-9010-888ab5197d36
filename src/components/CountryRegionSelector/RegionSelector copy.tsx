import React from 'react';
import { Cascader } from 'antd';
import type { CascaderProps } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';

interface CountryRegionOption {
  value: string;
  label: string;
  children?: CountryRegionOption[];
}

interface CountryRegionSelectorProps {
  value?: (string | number)[];
  onChange?: (
    value: (string | number)[],
    selectedOptions?: CountryRegionOption[]
  ) => void;
  placeholder?: string;
  disabled?: boolean;
  size?: 'small' | 'middle' | 'large';
  className?: string;
  allowClear?: boolean;
  showSearch?: boolean;
}

/**
 * 国家地区选择组件
 * 针对美国和中国客户优化的级联选择器
 */
const CountryRegionSelector: React.FC<CountryRegionSelectorProps> = ({
  value,
  onChange,
  placeholder,
  disabled = false,
  size = 'large',
  className = '',
  allowClear = true,
  showSearch = true,
}) => {
  const { t, isEnUS } = useLanguage();

  // 获取本地化的占位符
  const getPlaceholder = () => {
    if (placeholder) return placeholder;
    return t('common.form.countryRegionPlaceholder', '请选择国家和地区');
  };

  // 国家地区数据 - 优先显示美国和中国
  const countryRegionOptions: CountryRegionOption[] = [
    {
      value: 'US',
      label: isEnUS ? 'United States' : '美国',
      children: [
        { value: 'AL', label: isEnUS ? 'Alabama' : '阿拉巴马州' },
        { value: 'AK', label: isEnUS ? 'Alaska' : '阿拉斯加州' },
        { value: 'AZ', label: isEnUS ? 'Arizona' : '亚利桑那州' },
        { value: 'AR', label: isEnUS ? 'Arkansas' : '阿肯色州' },
        { value: 'CA', label: isEnUS ? 'California' : '加利福尼亚州' },
        { value: 'CO', label: isEnUS ? 'Colorado' : '科罗拉多州' },
        { value: 'CT', label: isEnUS ? 'Connecticut' : '康涅狄格州' },
        { value: 'DE', label: isEnUS ? 'Delaware' : '特拉华州' },
        { value: 'FL', label: isEnUS ? 'Florida' : '佛罗里达州' },
        { value: 'GA', label: isEnUS ? 'Georgia' : '乔治亚州' },
        { value: 'HI', label: isEnUS ? 'Hawaii' : '夏威夷州' },
        { value: 'ID', label: isEnUS ? 'Idaho' : '爱达荷州' },
        { value: 'IL', label: isEnUS ? 'Illinois' : '伊利诺伊州' },
        { value: 'IN', label: isEnUS ? 'Indiana' : '印第安纳州' },
        { value: 'IA', label: isEnUS ? 'Iowa' : '爱荷华州' },
        { value: 'KS', label: isEnUS ? 'Kansas' : '堪萨斯州' },
        { value: 'KY', label: isEnUS ? 'Kentucky' : '肯塔基州' },
        { value: 'LA', label: isEnUS ? 'Louisiana' : '路易斯安那州' },
        { value: 'ME', label: isEnUS ? 'Maine' : '缅因州' },
        { value: 'MD', label: isEnUS ? 'Maryland' : '马里兰州' },
        { value: 'MA', label: isEnUS ? 'Massachusetts' : '马萨诸塞州' },
        { value: 'MI', label: isEnUS ? 'Michigan' : '密歇根州' },
        { value: 'MN', label: isEnUS ? 'Minnesota' : '明尼苏达州' },
        { value: 'MS', label: isEnUS ? 'Mississippi' : '密西西比州' },
        { value: 'MO', label: isEnUS ? 'Missouri' : '密苏里州' },
        { value: 'MT', label: isEnUS ? 'Montana' : '蒙大拿州' },
        { value: 'NE', label: isEnUS ? 'Nebraska' : '内布拉斯加州' },
        { value: 'NV', label: isEnUS ? 'Nevada' : '内华达州' },
        { value: 'NH', label: isEnUS ? 'New Hampshire' : '新罕布什尔州' },
        { value: 'NJ', label: isEnUS ? 'New Jersey' : '新泽西州' },
        { value: 'NM', label: isEnUS ? 'New Mexico' : '新墨西哥州' },
        { value: 'NY', label: isEnUS ? 'New York' : '纽约州' },
        { value: 'NC', label: isEnUS ? 'North Carolina' : '北卡罗来纳州' },
        { value: 'ND', label: isEnUS ? 'North Dakota' : '北达科他州' },
        { value: 'OH', label: isEnUS ? 'Ohio' : '俄亥俄州' },
        { value: 'OK', label: isEnUS ? 'Oklahoma' : '俄克拉何马州' },
        { value: 'OR', label: isEnUS ? 'Oregon' : '俄勒冈州' },
        { value: 'PA', label: isEnUS ? 'Pennsylvania' : '宾夕法尼亚州' },
        { value: 'RI', label: isEnUS ? 'Rhode Island' : '罗德岛州' },
        { value: 'SC', label: isEnUS ? 'South Carolina' : '南卡罗来纳州' },
        { value: 'SD', label: isEnUS ? 'South Dakota' : '南达科他州' },
        { value: 'TN', label: isEnUS ? 'Tennessee' : '田纳西州' },
        { value: 'TX', label: isEnUS ? 'Texas' : '德克萨斯州' },
        { value: 'UT', label: isEnUS ? 'Utah' : '犹他州' },
        { value: 'VT', label: isEnUS ? 'Vermont' : '佛蒙特州' },
        { value: 'VA', label: isEnUS ? 'Virginia' : '弗吉尼亚州' },
        { value: 'WA', label: isEnUS ? 'Washington' : '华盛顿州' },
        { value: 'WV', label: isEnUS ? 'West Virginia' : '西弗吉尼亚州' },
        { value: 'WI', label: isEnUS ? 'Wisconsin' : '威斯康星州' },
        { value: 'WY', label: isEnUS ? 'Wyoming' : '怀俄明州' },
        { value: 'DC', label: isEnUS ? 'Washington, D.C.' : '华盛顿特区' },
      ],
    },
    {
      value: 'CN',
      label: isEnUS ? 'China' : '中国',
      children: [
        { value: 'BJ', label: isEnUS ? 'Beijing' : '北京市' },
        { value: 'SH', label: isEnUS ? 'Shanghai' : '上海市' },
        { value: 'TJ', label: isEnUS ? 'Tianjin' : '天津市' },
        { value: 'CQ', label: isEnUS ? 'Chongqing' : '重庆市' },
        { value: 'HE', label: isEnUS ? 'Hebei' : '河北省' },
        { value: 'SX', label: isEnUS ? 'Shanxi' : '山西省' },
        { value: 'NM', label: isEnUS ? 'Inner Mongolia' : '内蒙古自治区' },
        { value: 'LN', label: isEnUS ? 'Liaoning' : '辽宁省' },
        { value: 'JL', label: isEnUS ? 'Jilin' : '吉林省' },
        { value: 'HL', label: isEnUS ? 'Heilongjiang' : '黑龙江省' },
        { value: 'JS', label: isEnUS ? 'Jiangsu' : '江苏省' },
        { value: 'ZJ', label: isEnUS ? 'Zhejiang' : '浙江省' },
        { value: 'AH', label: isEnUS ? 'Anhui' : '安徽省' },
        { value: 'FJ', label: isEnUS ? 'Fujian' : '福建省' },
        { value: 'JX', label: isEnUS ? 'Jiangxi' : '江西省' },
        { value: 'SD', label: isEnUS ? 'Shandong' : '山东省' },
        { value: 'HA', label: isEnUS ? 'Henan' : '河南省' },
        { value: 'HB', label: isEnUS ? 'Hubei' : '湖北省' },
        { value: 'HN', label: isEnUS ? 'Hunan' : '湖南省' },
        { value: 'GD', label: isEnUS ? 'Guangdong' : '广东省' },
        { value: 'GX', label: isEnUS ? 'Guangxi' : '广西壮族自治区' },
        { value: 'HI', label: isEnUS ? 'Hainan' : '海南省' },
        { value: 'SC', label: isEnUS ? 'Sichuan' : '四川省' },
        { value: 'GZ', label: isEnUS ? 'Guizhou' : '贵州省' },
        { value: 'YN', label: isEnUS ? 'Yunnan' : '云南省' },
        { value: 'XZ', label: isEnUS ? 'Tibet' : '西藏自治区' },
        { value: 'SN', label: isEnUS ? 'Shaanxi' : '陕西省' },
        { value: 'GS', label: isEnUS ? 'Gansu' : '甘肃省' },
        { value: 'QH', label: isEnUS ? 'Qinghai' : '青海省' },
        { value: 'NX', label: isEnUS ? 'Ningxia' : '宁夏回族自治区' },
        { value: 'XJ', label: isEnUS ? 'Xinjiang' : '新疆维吾尔自治区' },
        { value: 'HK', label: isEnUS ? 'Hong Kong' : '香港特别行政区' },
        { value: 'MO', label: isEnUS ? 'Macau' : '澳门特别行政区' },
        { value: 'TW', label: isEnUS ? 'Taiwan' : '台湾省' },
      ],
    },
    {
      value: 'CA',
      label: isEnUS ? 'Canada' : '加拿大',
      children: [
        { value: 'AB', label: isEnUS ? 'Alberta' : '阿尔伯塔省' },
        {
          value: 'BC',
          label: isEnUS ? 'British Columbia' : '不列颠哥伦比亚省',
        },
        { value: 'MB', label: isEnUS ? 'Manitoba' : '曼尼托巴省' },
        { value: 'NB', label: isEnUS ? 'New Brunswick' : '新不伦瑞克省' },
        {
          value: 'NL',
          label: isEnUS ? 'Newfoundland and Labrador' : '纽芬兰和拉布拉多省',
        },
        { value: 'NS', label: isEnUS ? 'Nova Scotia' : '新斯科舍省' },
        { value: 'ON', label: isEnUS ? 'Ontario' : '安大略省' },
        {
          value: 'PE',
          label: isEnUS ? 'Prince Edward Island' : '爱德华王子岛省',
        },
        { value: 'QC', label: isEnUS ? 'Quebec' : '魁北克省' },
        { value: 'SK', label: isEnUS ? 'Saskatchewan' : '萨斯喀彻温省' },
        { value: 'NT', label: isEnUS ? 'Northwest Territories' : '西北地区' },
        { value: 'NU', label: isEnUS ? 'Nunavut' : '努纳武特地区' },
        { value: 'YT', label: isEnUS ? 'Yukon' : '育空地区' },
      ],
    },
    {
      value: 'GB',
      label: isEnUS ? 'United Kingdom' : '英国',
      children: [
        { value: 'ENG', label: isEnUS ? 'England' : '英格兰' },
        { value: 'SCT', label: isEnUS ? 'Scotland' : '苏格兰' },
        { value: 'WLS', label: isEnUS ? 'Wales' : '威尔士' },
        { value: 'NIR', label: isEnUS ? 'Northern Ireland' : '北爱尔兰' },
      ],
    },
    {
      value: 'AU',
      label: isEnUS ? 'Australia' : '澳大利亚',
      children: [
        { value: 'NSW', label: isEnUS ? 'New South Wales' : '新南威尔士州' },
        { value: 'VIC', label: isEnUS ? 'Victoria' : '维多利亚州' },
        { value: 'QLD', label: isEnUS ? 'Queensland' : '昆士兰州' },
        { value: 'WA', label: isEnUS ? 'Western Australia' : '西澳大利亚州' },
        { value: 'SA', label: isEnUS ? 'South Australia' : '南澳大利亚州' },
        { value: 'TAS', label: isEnUS ? 'Tasmania' : '塔斯马尼亚州' },
        {
          value: 'ACT',
          label: isEnUS ? 'Australian Capital Territory' : '澳大利亚首都特区',
        },
        { value: 'NT', label: isEnUS ? 'Northern Territory' : '北领地' },
      ],
    },
  ];

  // 搜索过滤函数
  const filter: CascaderProps<CountryRegionOption>['filter'] = (
    inputValue,
    path
  ) => {
    return path.some(option =>
      option.label.toLowerCase().includes(inputValue.toLowerCase())
    );
  };

  const cascaderClass = ['w-full', className].filter(Boolean).join(' ');

  return (
    <Cascader<CountryRegionOption>
      className={cascaderClass}
      options={countryRegionOptions}
      value={value}
      onChange={onChange}
      placeholder={getPlaceholder()}
      disabled={disabled}
      size={size}
      allowClear={allowClear}
      showSearch={showSearch ? { filter } : false}
      expandTrigger="hover"
      changeOnSelect={false}
      displayRender={labels => labels.join(' / ')}
      style={{ width: '100%' }}
    />
  );
};

export default CountryRegionSelector;
