import React from 'react';
import {
  ConfigProvider,
  Card,
  Form,
  Input,
  Button,
  Typography,
  message,
  Row,
  Col,
} from 'antd';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useRegisterStore } from '@/store/registerStore';
import FormButton from './coms/FormButton';
import CountrySelector from '@/components/CountryRegionSelector/CountrySelector';
import RegionSelector from '@/components/CountryRegionSelector/RegionSelector';
import PhoneInputFormItem from '@/components/PhoneInputFormItem/PhoneInputFormItem';

const PersonalInfo: React.FC = () => {
  const navigate = useNavigate();
  const { t, isEnUS } = useLanguage();
  const { formData, updateFormData, setCurrentStep } = useRegisterStore();
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    updateFormData(values);
    setCurrentStep(4);
    message.success('个人信息已保存');
    navigate('/register/password');
  };

  // 初始化表单值
  React.useEffect(() => {
    const initialValues = {
      firstName: formData.firstName,
      lastName: formData.lastName,
      phone: formData.phone,
      address: formData.address,
      // 处理国家和地区的初始值
      country:
        formData.country ||
        (formData.countryRegion && Array.isArray(formData.countryRegion)
          ? formData.countryRegion[0]
          : undefined),
      region:
        formData.region ||
        formData.state ||
        (formData.countryRegion && Array.isArray(formData.countryRegion)
          ? formData.countryRegion[1]
          : undefined),
      postalCode: formData.postalCode,
    };
    form.setFieldsValue(initialValues);
  }, [formData, form]);

  // 检查是否有前面步骤的数据，如果没有则重定向
  React.useEffect(() => {
    if (!formData.email) {
      message.warning(
        t('auth.register.step4.messages.emailVerificationRequired')
      );
      navigate('/register/email');
    } else if (!formData.alias) {
      message.warning(t('auth.register.step4.messages.aliasRequired'));
      navigate('/register/alias');
    }
  }, [formData.email, formData.alias, navigate]);

  return (
    <ConfigProvider
      theme={{
        components: {
          Select: {
            activeBorderColor: 'var(--color-form-item)',
            selectorBg: 'var(--color-form-item)',
            colorBorder: 'var(--color-form-item)',
            optionSelectedBg: 'var(--color-form-item)',
            borderRadius: 6,
            optionLineHeight: '54px',
            optionHeight: 54,
          },
        },
      }}
    >
      <h1 className="font-arial mb-2 text-center text-[32px] text-[#ff5e13] font-bold">
        {t('auth.register.step3.title')}
      </h1>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
      >
        <Form.Item
          label={t('auth.register.step3.form.firstName')}
          name="firstName"
          rules={[
            {
              required: true,
              message: t('auth.register.step3.form.firstNameRequired'),
            },
          ]}
        >
          <Input
            placeholder={t('auth.register.step3.form.firstNamePlaceholder')}
            className="placeholder:font-inter h-[54px] rounded-md border-none bg-form-item px-5 text-black placeholder:(text-[12px] text-black/25)"
            size="large"
          />
        </Form.Item>
        <Form.Item
          label={t('auth.register.step3.form.lastName')}
          name="lastName"
          rules={[
            {
              required: true,
              message: t('auth.register.step3.form.lastNameRequired'),
            },
          ]}
        >
          <Input
            placeholder={t('auth.register.step3.form.lastNamePlaceholder')}
            className="placeholder:font-inter h-[54px] rounded-md border-none bg-form-item px-5 text-black placeholder:(text-[12px] text-black/25)"
            size="large"
          />
        </Form.Item>

        <PhoneInputFormItem />

        <Form.Item label={t('auth.register.step3.form.address')} name="address">
          <Input
            placeholder={t('auth.register.step3.form.addressPlaceholder')}
            className="placeholder:font-inter h-[54px] rounded-md border-none bg-form-item px-5 text-black placeholder:(text-[12px] text-black/25)"
            size="large"
          />
        </Form.Item>

        <Form.Item label={t('auth.register.step3.form.country')} name="country">
          <CountrySelector
            placeholder={t('common.form.selectCountry')}
            size="large"
          />
        </Form.Item>
        <Form.Item label={t('common.form.selectRegion')} name="region">
          <RegionSelector
            form={form}
            placeholder={t('common.form.selectRegion')}
            size="large"
            countryFieldName="country"
            regionFieldName="region"
          />
        </Form.Item>

        <Form.Item
          label={t('auth.register.step3.form.postalCode')}
          name="postalCode"
        >
          <Input
            placeholder={t('auth.register.step3.form.postalCodePlaceholder')}
            className="placeholder:font-inter h-[54px] rounded-md border-none bg-form-item px-5 text-black placeholder:(text-[12px] text-black/25)"
            size="large"
          />
        </Form.Item>

        <FormButton text={t('auth.register.step3.buttons.next')} />
      </Form>
    </ConfigProvider>
  );
};

export default PersonalInfo;
