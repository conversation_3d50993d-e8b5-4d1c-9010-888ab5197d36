import type { Permission } from '@/router/routeMap';
// 注册用户表单数据类型
export interface RegisterFormData {
  email?: string;
  alias?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  address?: string;
  country?: string;
  state?: string;
  countryRegion?: (string | number)[];
  postalCode?: string;
  password?: string;
}

// 登录表单数据类型
export interface LoginFormData {
  email: string;
  password?: string;
  emailCode?: string;
}
// 用户相关类型
export enum UserRole {
  USER = 'user',
  MUSICIAN = 'musician',
  ADMIN = 'admin',
}

export interface User {
  id: string;
  email: string;
  alias: string;
  firstName: string;
  lastName: string;
  phone: string;
  address: string;
  country: string;
  state: string;
  postalCode: string;
  role: UserRole;
  avatar?: string;
  permissions: Permission[];
}

// 音频文件类型
export interface AudioFile {
  id: string;
  title: string;
  description: string;
  url: string;
  thumbnailUrl?: string;
  duration: number; // 秒
  fileSize: number; // 字节
  format: string;
  price: number;
  category: string;
  tags: string[];
  creatorId: string;
  creator: User;
  downloads: number;
  rating: number;
  createdAt: string;
  updatedAt: string;
}

// API 响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 表单验证错误类型
export interface FormError {
  field: string;
  message: string;
}

// 通用选项类型
export interface Option {
  label: string;
  value: string | number;
  disabled?: boolean;
}

// 文件上传类型
export interface UploadFile {
  uid: string;
  name: string;
  status: 'uploading' | 'done' | 'error';
  response?: any;
  url?: string;
  size?: number;
  type?: string;
}

// 搜索筛选类型
export interface SearchFilters {
  keyword?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  duration?: {
    min?: number;
    max?: number;
  };
  rating?: number;
  sortBy?:
    | 'newest'
    | 'oldest'
    | 'price_low'
    | 'price_high'
    | 'popular'
    | 'rating';
}

// 路由参数类型
export interface RouteParams {
  id?: string;
  page?: string;
  category?: string;
  [key: string]: string | undefined;
}
